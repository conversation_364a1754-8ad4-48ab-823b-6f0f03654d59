#include <iostream>
#include <map>
#include <string>

// 自定义比较器：按字符串长度排序
struct Comparator {
    bool operator()(const std::string& a, const std::string& b) const {
        return a.length() < b.length();
    }
};

int main() {
    // 使用自定义比较器的 map 容器
    // std::map<std::string, int, Comparator> myMap;
    std::map<std::string, int> myMap;
    // 插入三个元素
    myMap.insert({"hello", 1});  // 长度5
    myMap.insert({"world", 2});  // 长度5
    myMap.insert({"!",    3});   // 长度1
    
    // 遍历并输出 map 内容
    for(auto& pair : myMap) {
        std::cout << pair.first << ": " << pair.second << std::endl;
    }
    system("pause");
    
    return 0;
}