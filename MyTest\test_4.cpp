#include <iostream>
#include <vector>
#include <algorithm>
#include <climits>
using namespace std;

int main() {
    int n, m;
    cin >> n >> m;

    // 读取每个营地的补给包数量
    vector<int> supplies(n + 1);
    for (int i = 1; i <= n; i++) {
        cin >> supplies[i];
    }

    // 读取路径信息
    vector<vector<pair<int, int>>> paths(n + 1);
    for (int i = 0; i < m; i++) {
        int u, v, w;
        cin >> u >> v >> w;
        // w表示移动到该营地需要消耗的补给包数量
        paths[u].push_back({v, w});
        paths[v].push_back({u, w});
    }

    // 动态规划：dp[i]表示到达营地i时收集的总补给包数量
    vector<int> dp(n + 1, INT_MAX);
    dp[1] = supplies[1]; // 从第1座营地开始，收集营地1的补给包

    // 使用类似Dijkstra的方法
    vector<bool> visited(n + 1, false);

    for (int step = 0; step < n; step++) {
        int u = -1;
        // 找到未访问的、当前补给包数量最少的营地
        for (int i = 1; i <= n; i++) {
            if (!visited[i] && dp[i] != INT_MAX) {
                if (u == -1 || dp[i] < dp[u]) {
                    u = i;
                }
            }
        }

        if (u == -1) break;
        visited[u] = true;

        // 尝试移动到相邻营地
        for (auto& edge : paths[u]) {
            int v = edge.first;
            int w = edge.second; // 移动到营地v需要消耗的补给包数量

            if (!visited[v]) {
                // 检查是否有足够的补给包移动到营地v
                if (dp[u] >= w) {
                    // 可以移动：当前补给包 - 移动消耗 + 目标营地补给包
                    int new_supplies = dp[u] - w + supplies[v];
                    dp[v] = min(dp[v], new_supplies);
                }
            }
        }
    }

    // 输出到达第n座营地时的补给包数量
    if (dp[n] == INT_MAX) {
        cout << -1 << endl; // 无法到达
    } else {
        cout << dp[n] << endl;
    }

    return 0;
}