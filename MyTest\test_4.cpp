#include <iostream>
#include <vector>
#include <queue>
#include <climits>
#include <set>
#include <map>
using namespace std;

typedef long long ll;
const ll INF = LLONG_MAX;

struct State {
    int camp;
    ll capacity; // 当前背包容量（抵达该营地时补给包数量）

    State(int c, ll cap) : camp(c), capacity(cap) {}

    bool operator>(const State& other) const {
        return capacity > other.capacity;
    }
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int n, m;
    cin >> n >> m;

    vector<ll> A(n + 1);
    for (int i = 1; i <= n; i++) {
        cin >> A[i];
    }

    vector<vector<pair<int, ll>>> adj(n + 1);
    for (int i = 0; i < m; i++) {
        int u, v;
        ll w;
        cin >> u >> v >> w;
        adj[u].emplace_back(v, w);
    }

    // 存储每个节点的最优状态（按能力值ability=capacity+A[u]排序）
    vector<map<ll, ll>> nodeStates(n + 1);
    vector<ll> min_capacity(n + 1, INF);

    priority_queue<State, vector<State>, greater<State>> pq;
    min_capacity[1] = 0;
    pq.push(State(1, 0));

    // 插入状态并剔除被支配的状态
    auto insertState = [&](int u, ll cap) -> bool {
        ll ability = cap + A[u];
        auto& states = nodeStates[u];

        // 检查是否存在支配状态
        auto it = states.lower_bound(ability);
        if (it != states.end() && it->second <= cap) 
            return false;

        // 插入新状态
        states[ability] = cap;
        // 剔除被新状态支配的旧状态
        it = states.find(ability);
        while (it != states.begin()) {
            auto prev = it;
            --prev;
            if (prev->second >= cap) 
                states.erase(prev);
            else 
                break;
        }
        return true;
    };

    insertState(1, 0);

    while (!pq.empty()) {
        State cur = pq.top();
        pq.pop();
        int u = cur.camp;
        ll cap = cur.capacity;

        if (cap != min_capacity[u]) continue;
        if (u == n) {
            cout << cap << endl;
            return 0;
        }

        // 处理所有出边
        for (auto& edge : adj[u]) {
            int v = edge.first;
            ll w = edge.second;

            // 获取最小能满足w的能力值
            auto& states = nodeStates[u];
            auto it = states.lower_bound(w);
            if (it == states.end()) continue; // 无可用状态

            ll cur_cap = it->second; // 当前状态背包容量
            // 校验: 必须满足w <= cur_cap + A[u]
            if (w > cur_cap + A[u]) continue;

            // 新状态计算
            ll new_cap = max(cur_cap, w);
            if (new_cap >= min_capacity[v]) continue;

            // 松弛操作
            min_capacity[v] = new_cap;
            if (insertState(v, new_cap)) {
                pq.push(State(v, new_cap));
            }
        }
    }

    cout << -1 << endl;
    return 0;
}