#include <iostream>
#include <vector>
#include <algorithm>
#include <climits>
using namespace std;

int main() {
    int n, m;
    cin >> n >> m;

    // 读取每个营地的补给包数量
    vector<long long> supplies(n + 1);
    for (int i = 1; i <= n; i++) {
        cin >> supplies[i];
    }

    // 读取路径信息
    vector<vector<pair<int, int>>> paths(n + 1);
    for (int i = 0; i < m; i++) {
        int u, v, w;
        cin >> u >> v >> w;
        // w表示路径权重（可能是路径上的补给包消耗）
        paths[u].push_back({v, w});
        paths[v].push_back({u, w});
    }

    // 动态规划：dp[i]表示到达营地i时的最小补给包数量
    vector<long long> dp(n + 1, LLONG_MAX);
    dp[1] = supplies[1]; // 从第1座营地开始

    // 使用类似Dijkstra的方法，但这里是最小化补给包数量
    vector<bool> visited(n + 1, false);

    for (int step = 0; step < n; step++) {
        int u = -1;
        // 找到未访问的、当前补给包数量最少的营地
        for (int i = 1; i <= n; i++) {
            if (!visited[i] && dp[i] != LLONG_MAX) {
                if (u == -1 || dp[i] < dp[u]) {
                    u = i;
                }
            }
        }

        if (u == -1) break;
        visited[u] = true;

        // 更新相邻营地的最小补给包数量
        for (auto& edge : paths[u]) {
            int v = edge.first;
            int w = edge.second;
            if (!visited[v]) {
                // 回到基础理解：只累加营地补给包
                dp[v] = min(dp[v], dp[u] + supplies[v]);
            }
        }
    }

    // 输出到达第n座营地的最少补给包数量
    if (dp[n] == LLONG_MAX) {
        cout << -1 << endl; // 无法到达
    } else {
        cout << dp[n] << endl;
    }

    return 0;5 6
2 5 2 0 1
1 2 1
2 5 5
1 3 2
3 4 4
1 4 3
4 5 3
}