#include <iostream>
#include <vector>
#include <queue>
#include <climits>
using namespace std;

typedef long long ll;

struct State {
    int camp;      // 当前营地
    ll supplies;   // 当前背包容量

    State(int c, ll s) : camp(c), supplies(s) {}

    // 小顶堆：按背包容量升序
    bool operator>(const State& other) const {
        return supplies > other.supplies;
    }
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);

    int n, m;
    cin >> n >> m;

    vector<ll> A(n + 1);
    for (int i = 1; i <= n; i++) {
        cin >> A[i];
    }

    // 边界情况：如果只有一个营地
    if (n == 1) {
        cout << 0 << endl;
        return 0;
    }

    // 构建邻接表：adj[u] = { (v, w), ... }
    vector<vector<pair<int, ll>>> adj(n + 1);
    for (int i = 0; i < m; i++) {
        int u, v;
        ll w;
        cin >> u >> v >> w;
        adj[u].push_back({v, w});
        adj[v].push_back({u, w}); // 添加双向边
    }

    // Dijkstra初始化
    priority_queue<State, vector<State>, greater<State>> pq;
    vector<ll> min_supplies(n + 1, LLONG_MAX);  // 到达i营地的最小背包容量

    min_supplies[1] = 0;
    pq.push(State(1, 0));

    while (!pq.empty()) {
        State current = pq.top();
        pq.pop();
        int u = current.camp;
        ll s = current.supplies;

        // 跳过非最优状态
        if (s > min_supplies[u]) continue;

        // 遍历u的所有出边
        for (const auto& edge : adj[u]) {
            int v = edge.first;
            ll w = edge.second;

            // 规则校验：当前携带量s + A[u]必须≥边要求w
            if (w > s + A[u]) continue;

            // 计算新背包容量：max(s, w)（最小必要值）
            ll new_cap = max(s, w);

            // 松弛操作：更新v的最小背包容量
            if (new_cap < min_supplies[v]) {
                min_supplies[v] = new_cap;
                pq.push(State(v, new_cap));
            }
        }
    }

    // 检查是否能到达终点
    if (min_supplies[n] == LLONG_MAX) {
        cout << -1 << endl;
    } else {
        cout << min_supplies[n] << endl;
    }
    return 0;
}