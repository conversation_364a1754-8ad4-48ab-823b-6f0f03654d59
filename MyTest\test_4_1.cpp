#include <iostream>
#include <vector>
#include <queue>
#include <climits>
#include <algorithm>
using namespace std;

typedef long long ll;

struct State {
    int camp;      // 当前营地
    ll supplies;   // 当前背包容量
    
    State(int c, ll s) : camp(c), supplies(s) {}
    
    // 小顶堆：按背包容量升序排列
    bool operator>(const State& other) const {
        if (supplies != other.supplies) {
            return supplies > other.supplies;
        }
        return camp > other.camp; // 相同容量时按营地编号排序，保证稳定性
    }
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    
    int n, m;
    cin >> n >> m;
    
    // 输入验证
    if (n <= 0 || m < 0) {
        cout << -1 << endl;
        return 0;
    }
    
    vector<ll> A(n + 1);
    for (int i = 1; i <= n; i++) {
        cin >> A[i];
        // 确保补给量非负
        if (A[i] < 0) A[i] = 0;
    }
    
    // 构建邻接表：adj[u] = { (v, w), ... }
    vector<vector<pair<int, ll>>> adj(n + 1);
    for (int i = 0; i < m; i++) {
        int u, v;
        ll w;
        cin >> u >> v >> w;
        
        // 输入验证
        if (u < 1 || u > n || v < 1 || v > n || w < 0) {
            continue; // 跳过无效边
        }
        
        adj[u].emplace_back(v, w);
    }
    
    // 特殊情况：起点就是终点
    if (n == 1) {
        cout << 0 << endl;
        return 0;
    }
    
    // Dijkstra算法初始化
    priority_queue<State, vector<State>, greater<State>> pq;
    vector<ll> min_supplies(n + 1, LLONG_MAX);  // 到达i营地的最小背包容量
    vector<bool> visited(n + 1, false);         // 访问标记，避免重复处理
    
    min_supplies[1] = 0;
    pq.emplace(1, 0);
    
    while (!pq.empty()) {
        State current = pq.top();
        pq.pop();
        
        int u = current.camp;
        ll s = current.supplies;
        
        // 如果已经访问过，跳过
        if (visited[u]) continue;
        visited[u] = true;
        
        // 抵达终点时输出答案
        if (u == n) {
            cout << s << endl;
            return 0;
        }
        
        // 遍历u的所有出边
        for (const auto& [v, w] : adj[u]) {
            // 如果目标节点已访问，跳过
            if (visited[v]) continue;
            
            // 规则校验：当前携带量s + A[u]必须≥边要求w
            if (w > s + A[u]) continue;
            
            // 计算新背包容量：max(s, w)（到达v所需的最小必要容量）
            ll new_capacity = max(s, w);
            
            // 松弛操作：更新v的最小背包容量
            if (new_capacity < min_supplies[v]) {
                min_supplies[v] = new_capacity;
                pq.emplace(v, new_capacity);
            }
        }
    }
    
    // 无法抵达终点
    cout << -1 << endl;
    return 0;
}